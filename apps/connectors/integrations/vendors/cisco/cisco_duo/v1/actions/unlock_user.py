from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
)
from apps.connectors.integrations.actions.user import (
    UserLoginResult,
    UserLoginStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllUsers,
    ModifyUser,
)
from apps.accounts import Entitlement


class CiscoDuoV1UnlockUser(IntegrationAction):
    """Unlock a user in Cisco Duo (set status to active)."""
    
    name = "Unlock user"
    action_type = "cisco_duo/unlock_user"
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UserIdentifierArgs,
        result_type=UserLoginResult,
    )

    def execute(self, args: UserIdentifierArgs) -> UserLoginResult:
        api = self.integration.get_api()
        user_id = args.user_id.value
        
        # Unlock the user (set status to active)
        try:
            response = api.unlock_user(user_id)
            if "response" in response:
                user_data = response["response"]
                enabled = user_data.get("status", "").lower() == "active"
                return UserLoginResult(
                    result=UserLoginStatus(enabled=enabled)
                )
            else:
                return UserLoginResult(
                    error="Failed to unlock user",
                    result=UserLoginStatus(enabled=False)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to unlock user: {str(e)}",
                result=UserLoginStatus(enabled=False)
            )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllUsers, ModifyUser]
