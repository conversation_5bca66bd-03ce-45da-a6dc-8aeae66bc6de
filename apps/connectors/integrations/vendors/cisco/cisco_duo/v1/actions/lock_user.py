from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
)
from apps.connectors.integrations.actions.user import (
    UserLoginResult,
    UserLoginStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllUsers,
    ModifyUser,
)
from apps.accounts import Entitlement


class CiscoDuoV1LockUser(IntegrationAction):
    """Lock out a user in Cisco Duo."""
    
    name = "Lock user"
    action_type = "cisco_duo/lock_user"
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UserIdentifierArgs,
        result_type=UserLoginResult,
    )

    def execute(self, args: UserIdentifierArgs) -> UserLoginResult:
        api = self.integration.get_api()
        user_id = args.user_id.value
        
        # Lock the user
        try:
            response = api.lock_user(user_id)
            if "response" in response:
                user_data = response["response"]
                # Check if user is locked out (status should be "Locked Out")
                status = user_data.get("status", "").lower()
                enabled = status == "active"
                return UserLoginResult(
                    result=UserLoginStatus(enabled=enabled)
                )
            else:
                return UserLoginResult(
                    error="Failed to lock user",
                    result=UserLoginStatus(enabled=True)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to lock user: {str(e)}",
                result=UserLoginStatus(enabled=True)
            )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllUsers, ModifyUser]
