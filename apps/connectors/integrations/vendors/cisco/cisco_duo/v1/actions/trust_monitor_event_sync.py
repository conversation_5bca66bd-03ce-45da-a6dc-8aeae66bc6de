from typing import Generator
from datetime import datetime

from apps.connectors.integrations.actions.event_sync import EventSync, EventSyncArgs
from apps.connectors.integrations.schemas.event import Event, EventIOCInfo, VendorRef
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllUsers,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.actions.ocsf_mapping import (
    convert_trust_monitor_event_to_ocsf,
)


class CiscoDuoV1TrustMonitorEventSync(EventSync):
    """
    Event sync for Cisco Duo Trust Monitor events.
    
    This action fetches Trust Monitor events from Cisco Duo and converts them
    to OCSF Detection Finding format according to the detailed field mapping specification.
    """

    def execute(self, args: EventSyncArgs, **kwargs) -> Generator[Event, None, None]:
        api = self.integration.get_api()
        
        # Prepare API parameters for Trust Monitor events
        params = {}
        
        # Set time range if provided
        if args.since:
            # Trust Monitor API expects milliseconds
            params["mintime"] = int(args.since.timestamp() * 1000)
        
        # Add any additional parameters from kwargs
        params.update(kwargs)
        
        try:
            # Fetch Trust Monitor events from Duo API
            response = api.get_trust_monitor_events(**params)
            
            if "response" not in response:
                return
            
            # Process each Trust Monitor event
            for event_entry in response["response"]:
                try:
                    # Convert to OCSF format
                    ocsf_finding = convert_trust_monitor_event_to_ocsf(event_entry)
                    
                    # Parse event timestamp
                    event_timestamp = None
                    if event_entry.get("surfaced_timestamp"):
                        try:
                            # Trust Monitor timestamps are in milliseconds
                            event_timestamp = datetime.fromtimestamp(
                                event_entry["surfaced_timestamp"] / 1000
                            )
                        except (ValueError, TypeError):
                            pass
                    
                    # Create Event object
                    event = Event(
                        event_timestamp=event_timestamp,
                        raw_event=event_entry,
                        ocsf=ocsf_finding,
                        vendor_item_ref=VendorRef(
                            id=event_entry.get("sekey", ""),
                            title=f"Cisco Duo Trust Monitor - {event_entry.get('type', 'Unknown')} Event",
                            url=None,  # Duo doesn't provide direct URLs to Trust Monitor events
                            created=event_timestamp,
                        ),
                        vendor_group_ref=None,  # Trust Monitor events don't have group references
                        ioc=EventIOCInfo(
                            external_id=event_entry.get("type", ""),
                            external_name=f"Cisco Duo Trust Monitor {event_entry.get('type', 'Unknown')} Event",
                            has_ioc_definition=True,  # Trust Monitor events are anomaly detections
                            mitre_techniques=None,  # Duo doesn't provide MITRE technique mapping
                        ),
                    )
                    
                    yield event
                    
                except Exception as e:
                    # Log the error but continue processing other entries
                    # In a production environment, you might want to use proper logging
                    continue
                    
        except Exception as e:
            # Handle API errors gracefully
            # In a production environment, you might want to use proper logging
            return

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllUsers]
