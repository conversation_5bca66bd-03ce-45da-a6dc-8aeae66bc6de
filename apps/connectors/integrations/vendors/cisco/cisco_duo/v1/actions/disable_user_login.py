from apps.connectors.integrations.actions.user import (
    DisableUserLogin,
    UserLoginResult,
    UserLoginStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllUsers,
    ModifyUser,
)


class CiscoDuoV1DisableUserLogin(DisableUserLogin):
    def execute(self, args: UserIdentifierArgs) -> UserLoginResult:
        api = self.integration.get_api()
        user_id = args.user_id.value
        
        # First check if user exists
        try:
            user_response = api.get_user(user_id)
            if "response" not in user_response:
                return UserLoginResult(
                    error="User not found",
                    result=UserLoginStatus(enabled=False)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to get user: {str(e)}",
                result=UserLoginStatus(enabled=False)
            )
        
        # Disable the user
        try:
            response = api.disable_user(user_id)
            if "response" in response:
                user_data = response["response"]
                enabled = user_data.get("status", "").lower() == "active"
                return UserLoginResult(
                    result=UserLoginStatus(enabled=enabled)
                )
            else:
                return UserLoginResult(
                    error="Failed to disable user",
                    result=UserLoginStatus(enabled=True)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to disable user: {str(e)}",
                result=UserLoginStatus(enabled=True)
            )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllUsers, ModifyUser]
