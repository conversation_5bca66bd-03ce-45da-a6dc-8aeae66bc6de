from typing import Generator
from datetime import datetime

from apps.connectors.integrations.actions.event_sync import EventSync, EventSyncArgs
from apps.connectors.integrations.schemas.event import Event, EventIOCInfo, VendorRef
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllUsers,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.actions.ocsf_mapping import (
    convert_authentication_log_to_ocsf,
)


class CiscoDuoV1EventSync(EventSync):
    """
    Event sync for Cisco Duo authentication logs.
    
    This action fetches authentication events from Cisco Duo and converts them
    to OCSF format according to the detailed field mapping specification.
    """

    def execute(self, args: EventSyncArgs, **kwargs) -> Generator[Event, None, None]:
        api = self.integration.get_api()
        
        # Prepare API parameters for authentication logs
        params = {}
        
        # Set time range if provided
        if args.since:
            params["mintime"] = int(args.since.timestamp())
        
        # Add any additional parameters from kwargs
        params.update(kwargs)
        
        try:
            # Fetch authentication logs from Duo API
            response = api.get_authentication_logs(**params)
            
            if "response" not in response:
                return
            
            # Process each authentication log entry
            for log_entry in response["response"]:
                try:
                    # Convert to OCSF format
                    ocsf_auth = convert_authentication_log_to_ocsf(log_entry)
                    
                    # Parse event timestamp
                    event_timestamp = None
                    if log_entry.get("isotimestamp"):
                        try:
                            event_timestamp = datetime.fromisoformat(
                                log_entry["isotimestamp"].replace("Z", "+00:00")
                            )
                        except (ValueError, AttributeError):
                            pass
                    
                    if not event_timestamp and log_entry.get("timestamp"):
                        try:
                            event_timestamp = datetime.fromtimestamp(log_entry["timestamp"])
                        except (ValueError, TypeError):
                            pass
                    
                    # Create Event object
                    event = Event(
                        event_timestamp=event_timestamp,
                        raw_event=log_entry,
                        ocsf=ocsf_auth,
                        vendor_item_ref=VendorRef(
                            id=log_entry.get("txid", ""),
                            title=f"Cisco Duo Authentication - {log_entry.get('result', 'Unknown')}",
                            url=None,  # Duo doesn't provide direct URLs to individual auth events
                            created=event_timestamp,
                        ),
                        vendor_group_ref=None,  # Authentication logs don't have group references
                        ioc=EventIOCInfo(
                            external_id=log_entry.get("factor", ""),
                            external_name=f"Cisco Duo {log_entry.get('factor', 'Authentication')} - {log_entry.get('reason', '')}",
                            has_ioc_definition=False,
                            mitre_techniques=None,
                        ),
                    )
                    
                    yield event
                    
                except Exception as e:
                    # Log the error but continue processing other entries
                    # In a production environment, you might want to use proper logging
                    continue
                    
        except Exception as e:
            # Handle API errors gracefully
            # In a production environment, you might want to use proper logging
            return

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllUsers]
