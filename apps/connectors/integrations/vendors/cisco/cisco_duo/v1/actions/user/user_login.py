from typing import Sequence

from apps.connectors.integrations.actions.user.user_login import (
    DisableUserLogin,
    EnableUserLogin,
    UserLoginResult,
    UserLoginStatus,
)
from apps.connectors.integrations.health_check import IntegrationPermissionsHealthCheck


class ReadAllUsers(IntegrationPermissionsHealthCheck):
    """Health check to verify the integration can read all users."""
    
    @classmethod
    def check_name(cls):
        return "Can read all users"

    def check(self):
        api = self.integration.get_api()
        # Test with a small limit to verify we can read users
        response = api.get_users(limit=1)
        if "response" not in response:
            raise Exception("Unable to read users")
        return True


class ModifyUser(IntegrationPermissionsHealthCheck):
    """Health check to verify the integration can modify users."""
    
    @classmethod
    def check_name(cls):
        return "Can modify users"

    def check(self):
        # This is a more complex check that would require a test user
        # For now, we'll just check if we have the proper permissions
        # by attempting to read users (modify usually requires read)
        api = self.integration.get_api()
        response = api.get_users(limit=1)
        if "response" not in response:
            raise Exception("Unable to read users (required for modify operations)")
        return True


class CiscoDuoV1DisableUserLogin(DisableUserLogin):
    """Disable user login for Cisco Duo."""

    def execute(self, args, **kwargs) -> UserLoginResult:
        api = self.integration.get_api()
        user_id = args.user_id.value
        
        # First check if user exists
        try:
            user_response = api.get_user(user_id)
            if "response" not in user_response:
                return UserLoginResult(
                    error="User not found",
                    result=UserLoginStatus(enabled=False)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to get user: {str(e)}",
                result=UserLoginStatus(enabled=False)
            )
        
        # Disable the user
        try:
            response = api.disable_user(user_id)
            if "response" in response:
                user_data = response["response"]
                enabled = user_data.get("status", "").lower() == "active"
                return UserLoginResult(
                    result=UserLoginStatus(enabled=enabled)
                )
            else:
                return UserLoginResult(
                    error="Failed to disable user",
                    result=UserLoginStatus(enabled=True)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to disable user: {str(e)}",
                result=UserLoginStatus(enabled=True)
            )

    def get_permission_checks(self) -> Sequence[type[IntegrationPermissionsHealthCheck]]:
        return [ReadAllUsers, ModifyUser]


class CiscoDuoV1EnableUserLogin(EnableUserLogin):
    """Enable user login for Cisco Duo."""

    def execute(self, args, **kwargs) -> UserLoginResult:
        api = self.integration.get_api()
        user_id = args.user_id.value
        
        # First check if user exists
        try:
            user_response = api.get_user(user_id)
            if "response" not in user_response:
                return UserLoginResult(
                    error="User not found",
                    result=UserLoginStatus(enabled=False)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to get user: {str(e)}",
                result=UserLoginStatus(enabled=False)
            )
        
        # Enable the user
        try:
            response = api.enable_user(user_id)
            if "response" in response:
                user_data = response["response"]
                enabled = user_data.get("status", "").lower() == "active"
                return UserLoginResult(
                    result=UserLoginStatus(enabled=enabled)
                )
            else:
                return UserLoginResult(
                    error="Failed to enable user",
                    result=UserLoginStatus(enabled=False)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to enable user: {str(e)}",
                result=UserLoginStatus(enabled=False)
            )

    def get_permission_checks(self) -> Sequence[type[IntegrationPermissionsHealthCheck]]:
        return [ReadAllUsers, ModifyUser]
