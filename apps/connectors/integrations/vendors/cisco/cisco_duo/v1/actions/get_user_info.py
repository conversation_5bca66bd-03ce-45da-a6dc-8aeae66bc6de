from apps.connectors.integrations.actions.user import (
    GetUserInfo,
    UserInfoResult,
)
from apps.connectors.integrations.actions.utils import normalize_last_seen
from apps.connectors.integrations.schemas import UserIdentifierArgs, ocsf
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllUsers,
)


def normalize_user_info(user_data: dict) -> ocsf.User:
    """Normalize Cisco Duo user data to OCSF User format."""
    user = ocsf.User()
    
    if user_data.get("user_id"):
        user.uid = user_data["user_id"]
    
    if user_data.get("username"):
        user.name = user_data["username"]
    
    if user_data.get("email"):
        user.email_addr = user_data["email"]
    
    # Map user status to enabled flag
    status = user_data.get("status", "").lower()
    user.is_enabled = status == "active"
    
    # Add account information
    user.account = ocsf.Account(type="Cisco Duo Account")
    
    # Map user groups if available
    if user_data.get("groups"):
        user_groups = []
        for group in user_data["groups"]:
            if isinstance(group, dict):
                user_groups.append(ocsf.Group(name=group.get("name", "")))
            else:
                user_groups.append(ocsf.Group(name=str(group)))
        user.groups = user_groups
    
    return user


class CiscoDuoV1GetUserInfo(GetUserInfo):
    """Get user information from Cisco Duo."""

    def execute(self, args: UserIdentifierArgs) -> UserInfoResult:
        api = self.integration.get_api()
        user_id = args.user_id.value
        
        try:
            response = api.get_user(user_id)
            if "response" in response:
                user_data = response["response"]
                user_info = normalize_user_info(user_data)
                return UserInfoResult(result=user_info)
            else:
                return UserInfoResult(
                    error="User not found",
                    result=None
                )
        except Exception as e:
            return UserInfoResult(
                error=f"Failed to get user: {str(e)}",
                result=None
            )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllUsers]
