from apps.connectors.integrations.actions.user import (
    GetUserInfo,
    UserInfoResult,
)
from apps.connectors.integrations.actions.utils import normalize_last_seen
from apps.connectors.integrations.schemas import UserIdentifierArgs, ocsf
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllUsers,
)


def normalize_user_info(user_data: dict) -> ocsf.User:
    """Normalize Cisco Duo user data to OCSF User format."""
    return ocsf.User(
        uid=user_data.get("user_id"),
        name=user_data.get("username"),
        full_name=user_data.get("realname"),
        email_addr=user_data.get("email"),
        is_enabled=user_data.get("status", "").lower() == "active",
        last_login_time=normalize_last_seen(user_data.get("last_login")),
        type="User",
        type_id=1,  # OCSF type ID for User
    )


class CiscoDuoV1GetUserInfo(GetUserInfo):
    def execute(self, args: UserIdentifierArgs) -> UserInfoResult:
        api = self.integration.get_api()
        user_id = args.user_id.value
        
        try:
            response = api.get_user(user_id)
            if "response" in response:
                user_data = response["response"]
                user_info = normalize_user_info(user_data)
                return UserInfoResult(result=user_info)
            else:
                return UserInfoResult(
                    error="User not found",
                    result=None
                )
        except Exception as e:
            return UserInfoResult(
                error=f"Failed to get user: {str(e)}",
                result=None
            )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllUsers]
