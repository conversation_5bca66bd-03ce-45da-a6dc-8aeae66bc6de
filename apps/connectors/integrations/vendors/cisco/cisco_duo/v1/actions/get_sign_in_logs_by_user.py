from typing import List
from datetime import datetime
import time

from apps.connectors.integrations.actions.user import (
    GetSignInLogsByUser,
    SignInLogsByUserIdArgs,
    SignInLogsResult,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllUsers,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.actions.ocsf_mapping import (
    convert_authentication_log_to_ocsf,
)
from apps.connectors.integrations.schemas import ocsf


class CiscoDuoV1GetSignInLogsByUser(GetSignInLogsByUser):
    """Get sign-in logs by user ID for Cisco Duo."""

    def execute(self, args: SignInLogsByUserIdArgs) -> SignInLogsResult:
        api = self.integration.get_api()
        
        # Prepare API parameters
        params = {}
        
        # Add user filter if provided
        if args.user_id and args.user_id.value:
            params["users"] = args.user_id.value
        
        # Convert datetime to Unix timestamp for Duo API
        if args.start_time:
            params["mintime"] = int(args.start_time.timestamp())
        
        if args.end_time:
            params["maxtime"] = int(args.end_time.timestamp())
        
        try:
            # Fetch authentication logs from Duo API
            response = api.get_authentication_logs(**params)
            
            if "response" not in response:
                return SignInLogsResult(
                    error="No authentication logs found",
                    result=[]
                )
            
            # Convert each log entry to OCSF format
            ocsf_logs = []
            for log_entry in response["response"]:
                try:
                    ocsf_auth = convert_authentication_log_to_ocsf(log_entry)
                    ocsf_logs.append(ocsf_auth)
                except Exception as e:
                    # Log the error but continue processing other entries
                    # In a production environment, you might want to use proper logging
                    continue
            
            return SignInLogsResult(result=ocsf_logs)
            
        except Exception as e:
            return SignInLogsResult(
                error=f"Failed to fetch authentication logs: {str(e)}",
                result=[]
            )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllUsers]
