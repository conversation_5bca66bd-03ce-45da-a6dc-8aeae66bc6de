"""
OCSF mapping functions for Cisco Duo authentication logs and trust monitor events.

This module implements the detailed field mapping tables from the Cisco Duo
Data Integration Documentation for converting Duo API responses to OCSF format.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any

from apps.connectors.integrations.schemas import ocsf
from apps.connectors.integrations.actions.utils import to_list


def get_reason_action_disposition(reason: str) -> tuple[ocsf.ControlAction, ocsf.Disposition]:
    """
    Map Cisco Duo authentication reason to OCSF action and disposition.

    Based on the "Authentication Log Reason to action and disposition mapping" table
    from the Cisco Duo Data Integration Documentation.
    """
    reason_mapping = {
        # Denied reasons
        "user_marked_fraud": (ocsf.ControlAction.DENIED, ocsf.Disposition.ALERT),
        "deny_unenrolled_user": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "error": (ocsf.ControlAction.DENIED, ocsf.Disposition.ERROR),
        "locked_out": (ocsf.ControlAction.DENIED, ocsf.Disposition.DELAYED),
        "user_disabled": (ocsf.ControlAction.DENIED, ocsf.Disposition.ACCESS_REVOKED),
        "user_cancelled": (ocsf.ControlAction.DENIED, ocsf.Disposition.DROPPED),
        "invalid_passcode": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "no_response": (ocsf.ControlAction.DENIED, ocsf.Disposition.NO_ACTION),
        "no_keys_pressed": (ocsf.ControlAction.DENIED, ocsf.Disposition.NO_ACTION),
        "call_timed_out": (ocsf.ControlAction.DENIED, ocsf.Disposition.NO_ACTION),
        "location_restricted": (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        "factor_restricted": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "platform_restricted": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "version_restricted": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "rooted_device": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "no_screen_lock": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "touch_id_disabled": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "no_disk_encryption": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "anonymous_ip": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "out_of_date": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "denied_by_policy": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "software_restricted": (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        "no_duo_certificate_present": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "user_provided_invalid_certificate": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "could_not_determine_if_endpoint_was_trusted": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "frequent_attempts": (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        "invalid_management_certificate_collection_state": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "no_referring_hostname_provided": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "invalid_referring_hostname_provided": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "no_web_referer_match": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "endpoint_failed_google_verification": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "endpoint_is_not_trusted": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "invalid_device": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "endpoint_is_not_in_management_system": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "no_activated_duo_mobile_account": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),
        "queued_inflight_auth_expired": (ocsf.ControlAction.DENIED, ocsf.Disposition.DELAYED),
        "verification_code_missing": (ocsf.ControlAction.DENIED, ocsf.Disposition.REJECTED),
        "verification_code_incorrect": (ocsf.ControlAction.DENIED, ocsf.Disposition.UNAUTHORIZED),

        # Allowed reasons
        "allow_unenrolled_user": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        "bypass_user": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        "trusted_network": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        "remembered_device": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        "trusted_location": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        "user_approved": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        "valid_passcode": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        "allowed_by_policy": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        "allow_unenrolled_user_on_trusted_network": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        "user_not_in_permitted_group": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        "verification_code_correct": (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
    }

    return reason_mapping.get(reason, (ocsf.ControlAction.UNKNOWN, ocsf.Disposition.UNKNOWN))


def map_factor_to_ocsf(factor: str) -> tuple[ocsf.AuthFactorType, bool, bool]:
    """
    Map Cisco Duo authentication factor to OCSF factor type and HOTP/TOTP flags.

    Returns:
        tuple: (factor_type, is_hotp, is_totp)
    """
    factor_mapping = {
        # OTP factors
        "Desktop Authenticator": (ocsf.AuthFactorType.OTP, False, False),
        "duo_mobile_passcode_hotp": (ocsf.AuthFactorType.OTP, True, False),
        "duo_mobile_passcode_totp": (ocsf.AuthFactorType.OTP, False, True),
        "duo_mobile_passcode": (ocsf.AuthFactorType.OTP, False, False),
        "yubikey_code": (ocsf.AuthFactorType.OTP, False, False),

        # Push factors
        "duo_push": (ocsf.AuthFactorType.PUSH_NOTIFICATION, False, False),
        "Duo Push (passwordless)": (ocsf.AuthFactorType.PUSH_NOTIFICATION, False, False),
        "verified_duo_push": (ocsf.AuthFactorType.PUSH_NOTIFICATION, False, False),
        "Verified Duo Push (passwordless)": (ocsf.AuthFactorType.PUSH_NOTIFICATION, False, False),

        # Hardware tokens
        "hardware_token": (ocsf.AuthFactorType.HARDWARE_TOKEN, False, False),

        # SMS
        "sms_passcode": (ocsf.AuthFactorType.SMS, False, False),
        "sms_refresh": (ocsf.AuthFactorType.SMS, False, False),

        # WebAuthn
        "WebAuthn Chrome Touch ID": (ocsf.AuthFactorType.WEBAUTHN, False, False),
        "WebAuthn Credential": (ocsf.AuthFactorType.WEBAUTHN, False, False),
        "WebAuthn Security Key": (ocsf.AuthFactorType.WEBAUTHN, False, False),

        # Phone call
        "phone_call": (ocsf.AuthFactorType.PHONE_CALL, False, False),

        # Unknown/not available
        "not_available": (ocsf.AuthFactorType.UNKNOWN, False, False),
    }

    return factor_mapping.get(factor, (ocsf.AuthFactorType.UNKNOWN, False, False))


def map_result_to_status(result: str) -> ocsf.AuthenticationStatus:
    """Map Cisco Duo result to OCSF authentication status."""
    result_mapping = {
        "success": ocsf.AuthenticationStatus.SUCCESS,
        "failure": ocsf.AuthenticationStatus.FAILURE,
        "denied": ocsf.AuthenticationStatus.DENIED,
        "error": ocsf.AuthenticationStatus.ERROR,
        "fraud": ocsf.AuthenticationStatus.FRAUD,
    }

    return result_mapping.get(result, ocsf.AuthenticationStatus.UNKNOWN)


def convert_authentication_log_to_ocsf(event: dict) -> ocsf.Authentication:
    """
    Convert a Cisco Duo authentication log event to OCSF Authentication format.

    Implements the detailed field mapping table from the Cisco Duo documentation.
    """
    # Basic event information
    event_type = event.get("event_type", "")
    result = event.get("result", "")
    reason = event.get("reason", "")
    factor = event.get("factor", "")

    # Determine activity based on event type
    if event_type == "authentication":
        activity = ocsf.AuthenticationActivity.LOGON
        type_uid = ocsf.AuthenticationActivity.LOGON.id
    elif event_type == "enrollment":
        activity = ocsf.AuthenticationActivity.PREAUTH
        type_uid = ocsf.AuthenticationActivity.PREAUTH.id
    else:
        activity = ocsf.AuthenticationActivity.LOGON
        type_uid = ocsf.AuthenticationActivity.LOGON.id

    # Map result to status
    status = map_result_to_status(result)

    # Get action and disposition from reason
    action, disposition = get_reason_action_disposition(reason)

    # Map authentication factor
    factor_type, is_hotp, is_totp = map_factor_to_ocsf(factor)

    # Create authentication factor object
    auth_factor = ocsf.AuthenticationFactor(
        factor_type=factor_type,
        is_hotp=is_hotp if is_hotp else None,
        is_totp=is_totp if is_totp else None,
        provider="Cisco Duo"
    )

    # Parse timestamps
    time_dt = None
    if event.get("isotimestamp"):
        try:
            time_dt = datetime.fromisoformat(event["isotimestamp"].replace("Z", "+00:00"))
        except (ValueError, AttributeError):
            pass

    if not time_dt and event.get("timestamp"):
        try:
            time_dt = datetime.fromtimestamp(event["timestamp"])
        except (ValueError, TypeError):
            pass

    # Create OCSF Authentication object
    authentication = ocsf.Authentication(
        activity=activity,
        type_uid=type_uid,
        status=status,
        action=action,
        disposition=disposition,
        time_dt=time_dt,
        message=reason,
        status_detail=reason,
        auth_factors=[auth_factor],
        metadata=ocsf.Metadata(
            uid=event.get("txid"),
            correlation_uid=event.get("txid"),
            event_code="authentication",
            profiles=[],
        )
    )

    # Map access device information
    access_device = event.get("access_device", {})
    if access_device:
        src_endpoint = map_access_device_to_endpoint(access_device)
        authentication.src_endpoint = src_endpoint

    # Map authentication device information
    auth_device = event.get("auth_device")
    if auth_device:
        device = map_auth_device_to_device(auth_device)
        authentication.device = device

    # Map user information
    user_data = event.get("user", {})
    email = event.get("email", "")
    alias = event.get("alias", "")

    if user_data or email:
        user = map_user_data_to_user(user_data, email, alias)
        authentication.user = user

    # Map application information
    application = event.get("application", {})
    if application:
        service = ocsf.Service(
            uid=application.get("key"),
            name=application.get("name")
        )
        authentication.service = service

    # Map HTTP request information (browser data)
    if access_device.get("browser") or access_device.get("browser_version"):
        user_agent = f"{access_device.get('browser', '')} {access_device.get('browser_version', '')}".strip()
        if user_agent:
            authentication.http_request = ocsf.HttpRequest(user_agent=user_agent)

    return authentication


def map_access_device_to_endpoint(access_device: dict) -> ocsf.NetworkEndpoint:
    """Map Cisco Duo access_device to OCSF NetworkEndpoint."""
    endpoint = ocsf.NetworkEndpoint()

    # Basic device information
    if access_device.get("ip"):
        endpoint.ip = access_device["ip"]

    if access_device.get("hostname"):
        endpoint.hostname = access_device["hostname"]

    if access_device.get("epkey"):
        endpoint.uid = access_device["epkey"]

    # Operating system information
    os_name = access_device.get("os")
    os_version = access_device.get("os_version")
    if os_name or os_version:
        endpoint.os = ocsf.OperatingSystem(
            name=os_name,
            version=os_version
        )

    # Location information
    location = access_device.get("location", {})
    if location and any(location.values()):
        endpoint.location = ocsf.GeoLocation(
            city=location.get("city"),
            country=location.get("country"),
            region=location.get("state"),
            lat=location.get("latitude"),
            long=location.get("longitude")
        )

    # Security agents
    security_agents = access_device.get("security_agents", [])
    if security_agents:
        agents = []
        for agent in security_agents:
            if isinstance(agent, dict):
                agents.append(ocsf.Agent(
                    name=agent.get("security_agent"),
                    version=agent.get("version"),
                    type=ocsf.AgentType.ENDPOINT_DETECTION_AND_RESPONSE
                ))
        if agents:
            endpoint.agents_list = agents

    return endpoint


def map_auth_device_to_device(auth_device: dict) -> ocsf.Device:
    """Map Cisco Duo auth_device to OCSF Device."""
    device = ocsf.Device()

    if auth_device.get("ip"):
        device.ip = auth_device["ip"]

    if auth_device.get("key"):
        device.uid = auth_device["key"]

    if auth_device.get("name"):
        device.name = auth_device["name"]

    # Location information
    location = auth_device.get("location", {})
    if location and any(location.values()):
        device.location = ocsf.GeoLocation(
            city=location.get("city"),
            country=location.get("country"),
            region=location.get("state"),
            lat=location.get("latitude"),
            long=location.get("longitude")
        )

    return device


def map_user_data_to_user(user_data: dict, email: str = "", alias: str = "") -> ocsf.User:
    """Map Cisco Duo user data to OCSF User."""
    user = ocsf.User()

    if user_data.get("key"):
        user.uid = user_data["key"]

    if user_data.get("name"):
        user.name = user_data["name"]

    if email:
        user.email_addr = email

    if alias:
        user.uid_alt = alias

    # Map user groups
    groups = user_data.get("groups", [])
    if groups:
        user_groups = []
        for group_name in groups:
            user_groups.append(ocsf.Group(name=group_name))
        user.groups = user_groups

    return user


def convert_trust_monitor_event_to_ocsf(event: dict) -> ocsf.DetectionFinding:
    """
    Convert a Cisco Duo Trust Monitor event to OCSF Detection Finding format.

    Implements the detailed field mapping table for Trust Monitor events
    from the Cisco Duo documentation.
    """
    event_type = event.get("type", "")
    sekey = event.get("sekey", "")
    state = event.get("state", "")
    surfaced_timestamp = event.get("surfaced_timestamp")

    # Determine activity and status
    activity = ocsf.DetectionActivity.CREATE
    if state == "new":
        status = ocsf.DetectionStatus.NEW
    elif state == "processed":
        status = ocsf.DetectionStatus.RESOLVED
    else:
        status = ocsf.DetectionStatus.UNKNOWN

    # Create message based on event type
    if event_type == "auth":
        message = "Cisco Duo Trust Monitor Authentication Anomaly detected"
    elif event_type == "bypass_status":
        message = "Cisco Duo Trust Monitor bypass status event detected"
    elif event_type == "device_registration":
        message = "Cisco Duo Trust Monitor device registration anomaly detected"
    else:
        message = f"Cisco Duo Trust Monitor {event_type} event detected"

    # Parse timestamp
    time_dt = None
    if surfaced_timestamp:
        try:
            # Trust Monitor timestamps are in milliseconds
            time_dt = datetime.fromtimestamp(surfaced_timestamp / 1000)
        except (ValueError, TypeError):
            pass

    # Create finding information
    finding_info = ocsf.FindingInformation(
        uid=sekey,
        product=ocsf.Product(name="Cisco Duo"),
    )

    # Handle state_updated_timestamp
    if event.get("state_updated_timestamp"):
        try:
            finding_info.modified_time = datetime.fromtimestamp(
                event["state_updated_timestamp"] / 1000
            )
        except (ValueError, TypeError):
            pass

    # Create Detection Finding
    detection_finding = ocsf.DetectionFinding(
        activity=activity,
        status=status,
        message=message,
        time_dt=time_dt,
        finding_info=finding_info,
        is_suspected_breach=event.get("triaged_as_interesting", False),
        metadata=ocsf.Metadata(
            uid=sekey,
            correlation_uid=sekey,
            event_code=event_type,
            profiles=[],
        )
    )

    # Handle explanations (anomaly analysis)
    explanations = event.get("explanations", [])
    if explanations:
        anomalies = []
        for explanation in explanations:
            anomaly = ocsf.Anomaly(
                observations=[ocsf.Observable(
                    value=explanation.get("summary", ""),
                    type=ocsf.ObservableType.OTHER
                )],
                observation_pattern=explanation.get("type", "")
            )
            anomalies.append(anomaly)

        detection_finding.anomaly_analyses = [ocsf.AnomalyAnalysis(anomalies=anomalies)]

    # Handle bypass status events
    if event_type == "bypass_status":
        if event.get("bypass_status_enabled"):
            try:
                detection_finding.start_time = datetime.fromtimestamp(
                    event["bypass_status_enabled"] / 1000
                )
            except (ValueError, TypeError):
                pass

        # Map enabled_by and enabled_for to evidences
        evidences = []
        if event.get("enabled_by"):
            enabled_by = event["enabled_by"]
            evidences.append(ocsf.Evidence(
                actor=ocsf.Actor(
                    user=ocsf.User(
                        uid=enabled_by.get("key", ""),
                        name=enabled_by.get("name", "")
                    )
                )
            ))

        if event.get("enabled_for"):
            enabled_for = event["enabled_for"]
            evidences.append(ocsf.Evidence(
                user=ocsf.User(
                    uid=enabled_for.get("key", ""),
                    name=enabled_for.get("name", "")
                )
            ))

        if evidences:
            detection_finding.evidences = evidences

    # Handle surfaced_auth (related authentication event)
    surfaced_auth = event.get("surfaced_auth")
    if surfaced_auth and event_type == "auth":
        try:
            # Convert the related authentication event
            related_auth = convert_authentication_log_to_ocsf(surfaced_auth)

            # Add correlation UID to link the events
            if related_auth.metadata:
                related_auth.metadata.correlation_uid = sekey

            # Create related event reference
            related_event = ocsf.RelatedEvent(
                uid=related_auth.metadata.uid if related_auth.metadata else None,
                type_uid=related_auth.type_uid,
                type=related_auth.type_name if hasattr(related_auth, 'type_name') else None
            )

            detection_finding.finding_info.related_events = [related_event]

        except Exception:
            # If we can't process the related auth event, continue without it
            pass

    return detection_finding
