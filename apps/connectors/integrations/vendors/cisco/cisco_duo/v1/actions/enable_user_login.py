from apps.connectors.integrations.actions.user import (
    EnableUserLogin,
    UserLoginResult,
    UserLoginStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllUsers,
    ModifyUser,
)


class CiscoDuoV1EnableUserLogin(EnableUserLogin):
    def execute(self, args: UserIdentifierArgs) -> UserLoginResult:
        api = self.integration.get_api()
        user_id = args.user_id.value
        
        # First check if user exists
        try:
            user_response = api.get_user(user_id)
            if "response" not in user_response:
                return UserLoginResult(
                    error="User not found",
                    result=UserLoginStatus(enabled=False)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to get user: {str(e)}",
                result=UserLoginStatus(enabled=False)
            )
        
        # Enable the user
        try:
            response = api.enable_user(user_id)
            if "response" in response:
                user_data = response["response"]
                enabled = user_data.get("status", "").lower() == "active"
                return UserLoginResult(
                    result=UserLoginStatus(enabled=enabled)
                )
            else:
                return UserLoginResult(
                    error="Failed to enable user",
                    result=UserLoginStatus(enabled=False)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to enable user: {str(e)}",
                result=UserLoginStatus(enabled=False)
            )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllUsers, ModifyUser]
