from apps.connectors.integrations.actions.user import (
    EnableUserLogin,
    User<PERSON>ogin<PERSON><PERSON>ult,
    UserLoginStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllUsers,
    ModifyUser,
)


class CiscoDuoV1EnableUserLogin(EnableUserLogin):
    """Enable user login for Cisco Duo."""

    def execute(self, args: UserIdentifierArgs) -> UserLoginResult:
        api = self.integration.get_api()
        user_id = args.user_id.value
        
        # Enable the user
        try:
            response = api.enable_user(user_id)
            if "response" in response:
                user_data = response["response"]
                enabled = user_data.get("status", "").lower() == "active"
                return UserLoginResult(
                    result=UserLoginStatus(enabled=enabled)
                )
            else:
                return UserLoginResult(
                    error="Failed to enable user",
                    result=UserLoginStatus(enabled=False)
                )
        except Exception as e:
            return UserLoginResult(
                error=f"Failed to enable user: {str(e)}",
                result=UserLoginStatus(enabled=False)
            )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllUsers, ModifyUser]
