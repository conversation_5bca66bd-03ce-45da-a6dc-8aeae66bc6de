from apps.connectors.integrations import Integration

from .actions.disable_user_login import CiscoDuoV1DisableUserLogin
from .actions.enable_user_login import CiscoDuoV1EnableUserLogin
from .actions.get_sign_in_logs_by_user import CiscoDuoV1GetSignInLogsByUser
from .actions.get_user_info import CiscoDuoV1GetUserInfo
from .actions.host_sync import CiscoDuoV1HostSync
from .actions.lock_user import CiscoDuoV1LockUser
from .actions.unlock_user import CiscoDuoV1UnlockUser
from .api import CiscoDuoV1Api
from .health_check import ConnectionHealthCheck


class CiscoDuoV1Integration(Integration):
    api_class = CiscoDuoV1Api
    actions = (
        CiscoDuoV1HostSync,
        CiscoDuoV1DisableUserLogin,
        CiscoDuoV1EnableUserLogin,
        CiscoDuoV1GetUserInfo,
        <PERSON>iscoDuoV1<PERSON>ock<PERSON><PERSON>,
        <PERSON>iscoDuoV1UnlockUser,
        CiscoDuoV1GetSignInLogsByUser,
    )
    critical_health_checks = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,)
