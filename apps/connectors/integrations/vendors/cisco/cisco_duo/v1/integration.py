from apps.connectors.integrations import Integration

from .actions.disable_user_login import CiscoDuoV1DisableUserLogin
from .actions.enable_user_login import CiscoDuoV1EnableUserLogin
from .actions.get_user_info import CiscoDuoV1GetUserInfo
from .actions.host_sync import CiscoDuoV1HostSync
from .api import CiscoDuoV1Api
from .health_check import ConnectionHealthCheck


class CiscoDuoV1Integration(Integration):
    api_class = CiscoDuoV1Api
    actions = (
        CiscoDuoV1HostSync,
        CiscoDuoV1DisableUserLogin,
        CiscoDuoV1EnableUserLogin,
        CiscoDuoV1GetUserInfo,
    )
    critical_health_checks = (ConnectionHealthCheck,)
