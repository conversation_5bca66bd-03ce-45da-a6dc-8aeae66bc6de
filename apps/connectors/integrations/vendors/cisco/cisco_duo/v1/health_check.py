from requests import HTTPError

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.integrations.integration import IntegrationError


class ReadAllHosts(IntegrationPermissionsHealthCheck):
    name = "Read all hosts"
    description = "Read all hosts"
    value = "Read all hosts"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("get_endpoints", offset=0)
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class ReadAllUsers(IntegrationPermissionsHealthCheck):
    name = "Read all users"
    description = "Read all users"
    value = "Read all users"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            api = self.integration.get_api()
            api.get_users(limit=1)
            return IntegrationHealthCheckResult.PASSED
        except (IntegrationError, HTTPError):
            return IntegrationHealthCheckResult.FAILED


class ModifyUser(IntegrationPermissionsHealthCheck):
    name = "Modify users"
    description = "Modify users"
    value = "Modify users" 
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        # For now, we'll assume if we can read users, we can modify them
        # In a real implementation, you might want to test with a specific test user
        try:
            api = self.integration.get_api()
            api.get_users(limit=1)
            return IntegrationHealthCheckResult.PASSED
        except (IntegrationError, HTTPError):
            return IntegrationHealthCheckResult.FAILED


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            api.get_account_info()
            return IntegrationHealthCheckResult.PASSED
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED
