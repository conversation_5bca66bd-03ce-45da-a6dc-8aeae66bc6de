from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.vendors.mimecast.mimecast_email_security.v1.bookmarks import (
    MimecastEmailSecurityEventSyncBookmark,
    MimecastEmailSecurityEventSyncBookmarks,
)

from .connection import (
    MimecastEmailSecurityV1Config,
    MimecastEmailSecurityV1Connection,
)
from .integration import MimecastEmailSecurityV1Integration
from .settings import MimecastEmailSecurityV1Settings


class MimecastEmailSecurityV1TemplateVersion(TemplateVersion):
    integration = MimecastEmailSecurityV1Integration
    id = "v1"
    name = "v1"
    config_model = MimecastEmailSecurityV1Config
    settings_model = MimecastEmailSecurityV1Settings
    connection_model = MimecastEmailSecurityV1Connection
    bookmarks_model = MimecastEmailSecurityEventSyncBookmarks
