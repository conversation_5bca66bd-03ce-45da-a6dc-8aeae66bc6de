from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.mimecast.mimecast_email_security.v1.actions.decode_url import (
    MimecastEmailSecurityV1DecodeUrl,
)
from apps.connectors.integrations.vendors.mimecast.mimecast_email_security.v1.actions.event_sync import (
    MimecastEmailSecurityV1EventSync,
)

from .api import MimecastEmailSecurityV1Api
from .health_check import ReadLogs


class MimecastEmailSecurityV1Integration(Integration):
    api_class = MimecastEmailSecurityV1Api
    actions = (
        MimecastEmailSecurityV1EventSync,
        MimecastEmailSecurityV1DecodeUrl,
    )
    critical_health_checks = (ReadLogs,)
