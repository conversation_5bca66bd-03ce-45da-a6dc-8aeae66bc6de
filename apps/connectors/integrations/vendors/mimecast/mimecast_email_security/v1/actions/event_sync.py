from datetime import datetime, timedelta, timezone
from typing import Generator

from dateutil import parser

from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventSync,
    EventSyncArgs,
)
from apps.connectors.integrations.actions.utils import normalize, to_list
from apps.connectors.integrations.schemas.ocsf import (
    OSINT,
    ControlAction,
    Disposition,
    Email,
    EmailActivity,
    EmailActivityDirection,
    EmailActivityType,
    File,
    Fingerprint,
    HashAlgorithm,
    Metadata,
    NetworkActivity,
    NetworkActivityType,
    OSINTIndicatorType,
    Policy,
    Url,
    UrlCategory,
)
from apps.connectors.integrations.vendors.mimecast.mimecast_email_security.v1.api import (
    MimecastEmailSecurityV1Api,
    paginate,
)
from apps.connectors.integrations.vendors.mimecast.mimecast_email_security.v1.bookmarks import (
    MimecastEmailSecurityEventSyncBookmark,
)


def parse_datetime(dt_str: str) -> datetime:
    return parser.parse(dt_str)


def get_iso_date(dt_str: str) -> datetime:
    return parser.parse(dt_str).isoformat()


def get_action(action: str) -> ControlAction:
    action = action.lower()
    if action in ["blocked"]:
        return ControlAction.DENIED
    else:
        return ControlAction.UNKNOWN


def get_disposition(disposition: str) -> Disposition:
    disposition = disposition.lower()
    if disposition in ["block", "drop", "deny"]:
        return Disposition.DROPPED
    elif disposition in ["quarantine", "hold", "suspend"]:
        return Disposition.QUARANTINED
    elif disposition in ["allow", "approve"]:
        return Disposition.ALLOWED
    elif disposition in ["alert", "observe"]:
        return Disposition.ALERT
    elif disposition in ["reset"]:
        return Disposition.RESET
    else:
        return Disposition.UNKNOWN


def get_categories(categories: list[str]) -> list[str]:
    # Remove duplicates and sort categories
    mapped_categories = []
    for category in categories:
        if category == UrlCategory.PHISHING:  # Placeholder for actual category
            mapped_categories.append(UrlCategory.PHISHING)
        else:
            mapped_categories.append(UrlCategory.OTHER)
    return mapped_categories


def get_direction(route: str) -> EmailActivityDirection:
    if route == "inbound":
        return EmailActivityDirection.INBOUND
    elif route == "outbound":
        return EmailActivityDirection.OUTBOUND
    else:
        return EmailActivityDirection.UNKNOWN


def get_metadata(log: dict) -> Metadata:
    return Metadata(
        uid=log.get("id"),
        correlation_uid=log.get("id"),
        event_code=log.get("ttpDefinition"),
        profiles=[],
    )


def map_ttp_url_click_log(click: dict) -> NetworkActivity:
    return NetworkActivity(
        activity=NetworkActivityType.UNKNOWN,
        url=Url(
            categories=get_categories(to_list(click["category"])),
            url_string=click.get("url"),
        ),
        osint=[
            OSINT(
                email=Email(
                    to=to_list(click.get("userEmailAddress")),
                ),
                value="",
            )
        ],
        action=get_action(click.get("action")),
        disposition=get_disposition(click.get("adminOverride")),
        time_dt=get_iso_date(click.get("date")),
        message=click.get("scanResult"),
        metadata=get_metadata(click),
    )


def map_ttp_attachment_log(log: dict) -> EmailActivity:
    file_hash = log.get("fileHash")
    file_obj = File(
        name=log.get("fileName"),
        mime_type=log.get("fileType"),
        hashes=[Fingerprint(algorithm=HashAlgorithm.SHA256, value=file_hash)]
        if file_hash
        else [],
    )
    return EmailActivity(
        action=log.get("actionTriggered"),
        disposition=log.get("result"),
        activity=EmailActivityType.OTHER,
        email=Email(
            from_mailbox=log.get("senderAddress"),
            to=[log.get("recipientAddress")],
            files=[file_obj],
        ),
        time_dt=get_iso_date(log.get("date")),
        message=log.get("details"),
        direction=get_direction(log.get("route")),
        metadata=get_metadata(log),
    )


def get_impersonation_type(imp_type: str) -> OSINTIndicatorType:
    if imp_type in [
        "similar_internal_domain",
        "newly_observed_domain",
        "custom_external_domain",
        "mimecast_external_domain",
        "advanced_similar_internal_domain",
        "advanced_custom_external_domain",
        "advanced_mimecast_external_domain",
    ]:
        return OSINTIndicatorType.DOMAIN
    elif imp_type in [
        "internal_user_name",
        "reply_address_mismatch",
        "custom_name_list",
    ]:
        return OSINTIndicatorType.EMAIL_ADDRESS
    else:
        return OSINTIndicatorType.OTHER


def map_ttp_impersonation_log(log: dict) -> EmailActivity:
    osint = []
    for imp in log.get("impersonationResults", []):
        imp_type = imp.get("impersonationDomainSource")
        value = imp.get("stringSimilarToDomain")
        osint.append(
            OSINT(uid=imp_type, type=get_impersonation_type(imp_type), value=value)
        )

    return EmailActivity(
        action=log.get("action"),
        activity=NetworkActivityType.OTHER,
        email=Email(
            from_mailbox=log.get("senderAddress"),
            to=to_list(log.get("recipientAddress")),
            subject=log.get("subject"),
            message_uid=log.get("messageId"),
            x_originating_ip=to_list(log.get("senderIpAddress")),
        ),
        time_dt=get_iso_date(log.get("eventTime")),
        message=log.get("definition"),
        metadata=get_metadata(log),
        osint=osint,
        policy=Policy(
            name=log.get("definition"),
        ),
    )


def convert_to_ocsf(event: dict) -> EmailActivity:
    log_type = event.get("log_type", "unknown")
    event = event.get("event", {})
    if log_type == "clickLogs":
        return map_ttp_url_click_log(event)
    if log_type == "attachmentLogs":
        return map_ttp_attachment_log(event)
    if log_type == "impersonationLogs":
        return map_ttp_impersonation_log(event)


def normalize_event(event: dict) -> Event:
    event_ = event.get("event", {})
    return Event(
        event_timestamp=get_iso_date(event_.get("date") or event_.get("eventTime")),
        raw_event=event,
        ocsf=convert_to_ocsf(event),
    )


def get_start_end_dates(
    bookmark: MimecastEmailSecurityEventSyncBookmark, type: str
) -> tuple[str, str]:
    log_type_start = bookmark.time_range_start.get(type)
    if not log_type_start:
        # Default to one day ago if no timestamp exists for this log type
        log_type_start = (
            (datetime.now(timezone.utc) - timedelta(days=1))
            .isoformat()
            .replace("+00:00", "Z")
        )

    return log_type_start


def set_bookmark_time(
    bookmark: MimecastEmailSecurityEventSyncBookmark,
    log_type: str,
    time_stamp: datetime,
) -> None:
    bookmark.time_range_start[log_type] = time_stamp.isoformat().replace("+00:00", "Z")


def process_event(
    bookmark: MimecastEmailSecurityEventSyncBookmark,
    log_type: str,
    bound_method,
):
    log_type_start = get_start_end_dates(bookmark, log_type)
    response = paginate(
        bound_method=bound_method,
        from_date=parse_datetime(log_type_start).isoformat(),
        to_date=datetime.now().isoformat(),
    )
    time_stamp = parse_datetime(log_type_start)
    for data in response:
        for item in data:
            for event in item.get(log_type, {}):
                yield {"event": event, "log_type": log_type}
                if time_stamp > parse_datetime(
                    event.get("date") or event.get("eventTime")
                ):
                    time_stamp = parse_datetime(
                        event.get("date") or event.get("eventTime")
                    )
    set_bookmark_time(bookmark, log_type, time_stamp)


class MimecastEmailSecurityV1EventSync(EventSync):
    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: MimecastEmailSecurityEventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: MimecastEmailSecurityV1Api = self.integration.get_api()
        # --- TTP URL Logs ---
        yield from process_event(
            bookmark,
            log_type="clickLogs",
            bound_method=api.get_ttp_url_logs,
        )

        yield from process_event(
            bookmark,
            log_type="attachmentLogs",
            bound_method=api.get_ttp_attachment_logs,
        )

        yield from process_event(
            bookmark,
            log_type="impersonationLogs",
            bound_method=api.get_ttp_impersonation_logs,
        )

    def get_permission_checks(self):
        return []  # pragma: no cover
