from apps.connectors.integrations.actions.decode_url import (
    DecodeUrl,
    DecodeUrlArgs,
    DecodeUrlResult,
)
from apps.connectors.integrations.schemas.ocsf import Url
from apps.connectors.integrations.vendors.mimecast.mimecast_email_security.v1.api import (
    MimecastEmailSecurityV1Api,
)


class MimecastEmailSecurityV1DecodeUrl(DecodeUrl):
    """
    Decode a URL using the Mimecast API.
    This action sends a request to the Mimecast API to decode the provided URL.
    """

    def execute(self, args: DecodeUrlArgs) -> DecodeUrlResult:
        api: MimecastEmailSecurityV1Api = self.integration.get_api()
        result = api.decode_url(args.url.value)

        # Extract the decoded URL from the response
        # The API returns: {"data": [{"url": "decoded_url", "success": true}], "meta": {"status": 200}}
        data = result.get("data", [])
        if data and len(data) > 0:
            decoded_url = data[0].get("url", "")
            success = data[0].get("success", False)

            if success and decoded_url:
                return DecodeUrlResult(result=Url(url_string=decoded_url))

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return []
