import base64
import hashlib
import hmac
import time

from apps.connectors.integrations.api import ApiBase


def get_params(page_size=100, page_token=None, from_date=None, to_date=None):
    pagination = {}
    if page_size:
        pagination["pageSize"] = page_size
    if page_token:
        pagination["pageToken"] = page_token
    data = {}
    if from_date:
        data["from"] = from_date
    if to_date:
        data["to"] = to_date
    params = {"meta": {"pagination": pagination}, "data": [data]}
    return params


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    response = response.get("result", response)
    results = response["data"]
    yield results
    items_count = len(results)
    page_token = response.get("meta", {}).get("pagination", {}).get("next", None)

    while items_count > 0 and page_token is not None:
        response = bound_method(page_token=page_token, **kwargs)
        response = response.get("result", response)
        results = response["data"]
        yield results
        items_count = len(results)
        page_token = response.get("meta", {}).get("pagination", {}).get("next", None)


class MimecastEmailSecurityV1Api(ApiBase):
    def __init__(self, base_url=None, app_id=None, username=None, password=None):
        self.app_id = app_id
        self.username = username
        self.password = password
        self.access_key = None
        self.secret_key = None
        user_pass = f"{self.username}:{self.password}"
        encoded = base64.b64encode(user_pass.encode()).decode()
        super().__init__(
            base_url=base_url,
            static_headers={
                "x-mc-app-id": self.app_id,
                "Content-Type": "application/json",
                "Authorization": f"Basic-Cloud {encoded}",
            },
        )

    def get_authorization_headers(self, path: str) -> dict:
        """
        Generate HMAC-based authorization headers for Mimecast API requests as per:
        https://integrations.mimecast.com/documentation/api-overview/authorization/
        """
        if self.access_key and self.secret_key:
            pass
        else:
            url = self.url("api/login")
            data = {"data": [{"username": self.username}]}
            response = self.session.post(url, json=data).json()
            self.access_key = response.get("data", [{}])[0].get("accessKey")
            self.secret_key = response.get("data", [{}])[0].get("secretKey")

        timestamp = str(int(time.time() * 1000))
        request_id = timestamp  # can be improved for uniqueness
        data_to_sign = f"{timestamp}:{request_id}:{self.app_id}:{path}"
        hmac_sha1 = hmac.new(
            self.secret_key.encode(), data_to_sign.encode(), hashlib.sha1
        )
        signature = base64.b64encode(hmac_sha1.digest()).decode()
        return {
            "Authorization": f"MC {self.access_key}:{signature}",
            "x-mc-app-id": self.app_id,
            "x-mc-date": timestamp,
            "x-mc-req-id": request_id,
            "Content-Type": "application/json",
        }

    def message_finder_search(self, **kwargs):
        """
        Call the Message Finder (Tracking) API: /api/message-finder/search
        https://integrations.mimecast.com/documentation/endpoint-reference/message-finder-formerly-tracking/search/
        """
        path = "/api/message-finder/search"
        headers = self.get_authorization_headers(path)
        url = self.url(path)
        params = {}
        response = self.session.post(url, headers=headers, json=params)
        return response.json()

    def get_siem_logs(self, **kwargs):
        """
        Call the SIEM Logs API: /api/audit/get-siem-logs
        https://integrations.mimecast.com/documentation/endpoint-reference/logs-and-statistics/get-siem-logs/
        """
        path = "/api/audit/get-siem-logs"
        headers = self.get_authorization_headers(path)
        url = self.url(path)
        response = self.session.post(url, headers=headers, json=kwargs)
        return response.json()

    def get_ttp_url_logs(
        self, page_size=100, page_token=None, from_date=None, to_date=None, **kwargs
    ):
        """
        Call the TTP URL Logs API: /api/ttp/url/get-logs
        https://integrations.mimecast.com/documentation/endpoint-reference/logs-and-statistics/get-ttp-url-logs/
        """
        path = "/api/ttp/url/get-logs"
        headers = self.get_authorization_headers(path)
        url = self.url(path)
        response = self.session.post(
            url,
            headers=headers,
            json=get_params(page_size, page_token, from_date, to_date),
        )
        return response.json()

    def get_ttp_attachment_logs(
        self, page_size=100, page_token=None, from_date=None, to_date=None, **kwargs
    ):
        """
        Call the TTP Attachment Protection Logs API: /api/ttp/attachment/get-logs
        https://integrations.mimecast.com/documentation/endpoint-reference/logs-and-statistics/get-ttp-attachment-protection-logs/
        """
        path = "/api/ttp/attachment/get-logs"
        headers = self.get_authorization_headers(path)
        url = self.url(path)
        response = self.session.post(
            url,
            headers=headers,
            json=get_params(page_size, page_token, from_date, to_date),
        )
        return response.json()

    def get_ttp_impersonation_logs(
        self, page_size=100, page_token=None, from_date=None, to_date=None, **kwargs
    ):
        """
        Call the TTP Impersonation Protection Logs API: /api/ttp/impersonation/get-logs
        https://integrations.mimecast.com/documentation/endpoint-reference/logs-and-statistics/get-ttp-impersonation-protect-logs/
        """
        path = "/api/ttp/impersonation/get-logs"
        headers = self.get_authorization_headers(path)
        url = self.url(path)
        response = self.session.post(
            url,
            headers=headers,
            json=get_params(page_size, page_token, from_date, to_date),
        )
        return response.json()

    def decode_url(self, url_to_decode):
        """
        Call the TTP URL Decode API: /api/ttp/url/decode-url
        https://integrations.mimecast.com/documentation/endpoint-reference/targeted-threat-protection-url-protect/decode-url/
        """
        path = "/api/ttp/url/decode-url"
        headers = self.get_authorization_headers(path)
        api_url = self.url(path)
        data = {"data": [{"url": url_to_decode}]}
        response = self.session.post(api_url, headers=headers, json=data)
        return response.json()
