from pydantic import Field

from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.bookmarks import create_bookmarks_model
from apps.connectors.integrations.template import TemplateVersionActionBookmark


class MimecastEmailSecurityEventSyncBookmark(TemplateVersionActionBookmark):
    time_range_start: dict = Field(
        title="Time Range Start Datetime",
        description="The start of the time range for the event sync.",
        default_factory=dict,
    )


MimecastEmailSecurityEventSyncBookmarks = create_bookmarks_model(
    "MimecastEmailSecurityEventSyncBookmarks",
    {
        IntegrationActionType.EVENT_SYNC: MimecastEmailSecurityEventSyncBookmark,
    },
)
