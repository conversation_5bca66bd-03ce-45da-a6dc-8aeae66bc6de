from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MimecastEmailSecurityV1TemplateVersion


class MimecastEmailSecurityTemplate(Template):
    id = "mimecast_email_security"
    name = "Mimecast Advanced Email Security"
    category = Template.Category.EMAIL_SECURITY
    versions = {
        MimecastEmailSecurityV1TemplateVersion.id: MimecastEmailSecurityV1TemplateVersion(),
    }
    vendor = Vendors.MIMECAST
