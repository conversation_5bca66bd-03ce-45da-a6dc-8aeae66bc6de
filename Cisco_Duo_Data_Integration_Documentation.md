# DI - Cisco Duo

## Cisco Identity Intelligence (Duo) API Integration and OCSF Mapping

### Overview

Cisco Identity Intelligence (formerly Duo Security) is a cloud-based multi-factor authentication (MFA) solution that provides secure access to applications through a simple "push" notification to mobile devices. This integration focuses on ingesting authentication events from Cisco Duo's API into our data connector framework and transforming them into the OCSF (Open Cybersecurity Schema Framework) format.

### Security Events/Logs Provided

Cisco Duo provides several types of security events and logs through their Admin API:

1. **Authentication Logs**: Records of all authentication attempts, including successes and failures
2. **Trust Monitor Events**: Suspicious authentication activity detected by <PERSON>'s Trust Monitor

### Value Proposition

This integration enables:
- Comprehensive visibility into authentication attempts across all protected applications
- Correlation of MFA events with other security telemetry
- Identification of authentication patterns and potential security threats
- Compliance reporting with standardized event formats via OCSF

## API Endpoints and Authentication

### Modify User API Endpoint

Endpoint to disable or lockout the user using Duo assigned user id:

```
POST /admin/v1/users/[user_id]
```

This endpoint returns 200 if the user was modified successfully, 400 for invalid or missing parameters and 404 if the user is not found.

### Authentication Logs API Endpoints

The primary API endpoint for retrieving authentication events is:

```
GET /admin/v1/logs/authentication
```

This endpoint returns authentication log events, which include successful and failed authentication attempts.

#### Additional Related Endpoints

```
GET /admin/v1/trust_monitor/events
```
Trust Monitor security events

### Trust Monitor API Details

Trust Monitor is Cisco Duo's security analytics tool that detects suspicious and potentially malicious authentications. The API endpoint for retrieving Trust Monitor events is:

```
GET /admin/v1/trust_monitor/events
```

This endpoint supports the following query parameters:
- **mintime**: Filter events that occurred after this Unix timestamp
- **maxtime**: Filter events that occurred before this Unix timestamp
- **type**: Filter events by type (e.g., "auth", "bypass_status", "device_registration")
- **limit**: Maximum number of events to return (default: 50, max: 200)
- **offset**: Start position of events to return for pagination

Like other Duo API endpoints, Trust Monitor uses pagination with a combination of timestamp and entry_offset for continuation tokens.

### Authentication Mechanism

Cisco Duo uses a custom authentication scheme for its Admin API:

1. Each request must include:
   - **Integration Key (ikey)** - identifies the integration
   - **Secret Key (skey)** - used to sign requests
   - **API Hostname** - unique to each Duo account

2. Authentication uses HMAC-SHA1 signature with the following parameters:
   - Date header (RFC 2822 format)
   - HTTP Method (GET, POST, etc.)
   - Duo API hostname
   - Request path
   - Query parameters (sorted alphanumerically)

3. The Authorization header uses Basic auth format with the integration key and signature.

### API Limitations

**Rate Limits:**
- 2 requests per second per integration key
- Maximum of 60 requests per minute

**Pagination:**
- Default limit of 1000 records per response
- Uses offset-based pagination

**Date Range:**
- Can query up to 180 days of historical data
- Real-time events may have up to a 5-minute delay

## Actions

| Portal Action (TAP) | Type | Data Connector Action Identifier | Operates on Class (Entity) | Target Type (Entity targeted by this action) | API Call | Notes |
|---------------------|------|----------------------------------|----------------------------|----------------------------------------------|----------|-------|
| disable_user | Response | disable_user | UserIdentifier | User | https://duo.com/docs/adminapi#modify-user | Payload should contain status=disable |
| lock_user | Response | lock_user | UserIdentifier | User | https://duo.com/docs/adminapi#modify-user | Payload should contain status=locked_out |
| unlock_user | Response | unlock_user | UserIdentifier | User | https://duo.com/docs/adminapi#modify-user | Payload should contain status=active |
| get_sign_in_logs_by_user | Investigation | get_sign_in_logs_by_user | UserIdentifier | | https://duo.com/docs/adminapi#authentication-logs | Fetch logs by the following params: users, mintime and maxtime. users takes a user_id. Normalize the results using: DI - Cisco Duo \| [inlineExtension]Detailed Field Mapping Table Authentication Logs |

## JSON Response Examples

### Authentication Logs Response

Below is a complete example of an authentication logs response from the Duo Admin API, including all fields as documented in the official API documentation:

```json
{
  "response": [
    {
      "access_device": {
        "browser": "Chrome",
        "browser_version": "91.0.4472",
        "epkey": "D1234567890ABCDEF1234567890ABCDEF",
        "flash_version": null,
        "hostname": null,
        "ip": "***********",
        "is_encryption_enabled": true,
        "is_firewall_enabled": true,
        "is_password_set": true,
        "java_version": null,
        "location": {
          "city": "Ann Arbor",
          "country": "United States",
          "state": "Michigan",
          "longitude": -83.7430,
          "latitude": 42.2808
        },
        "os": "Mac OS X",
        "os_version": "10.15.7",
        "security_agents": []
      },
      "adaptive_trust_assessments": {
        "on_demand": {
          "features": [
            {
              "key": "user_location",
              "name": "User Location",
              "score": 0.5
            },
            {
              "key": "device_state",
              "name": "Device Security Posture",
              "score": 0.85
            }
          ],
          "status": "completed",
          "threat_level": "low",
          "trust_level": "trusted"
        }
      },
      "alias": "",
      "application": {
        "key": "DIY12345ABCDEFGHIJKLM",
        "name": "Company VPN"
      },
      "auth_context": {
        "additional_context": "Windows host",
        "auth_stage": "mfa",
        "clientless": false,
        "trusted_endpoint": false
      },
      "auth_device": {
        "ip": null,
        "key": "DPSTGE71N9Z1Q9NKGNX8",
        "location": {
          "city": null,
          "country": null,
          "state": null,
          "longitude": null,
          "latitude": null
        },
        "name": "iPhone 12 (iOS 14.5.1)"
      },
      "email": "<EMAIL>",
      "event_type": "authentication",
      "factor": "duo_push",
      "isotimestamp": "2021-05-24T14:22:23.000Z",
      "ood_software": false,
      "reason": "user_approved",
      "result": "success",
      "timestamp": 1621876543,
      "trusted_endpoint_status": "not trusted",
      "txid": "1234abcd-1234-abcd-1234-1234abcd1234",
      "user": {
        "groups": [
          "VPN Users",
          "Engineering"
        ],
        "key": "DUOUSER1234ABCDEF",
        "name": "<EMAIL>"
      },
      "policy_key": "DPMA1234567890ABCDEFG"
    },
    {
      "access_device": {
        "browser": "Chrome",
        "browser_version": "90.0.4430",
        "epkey": null,
        "flash_version": null,
        "hostname": null,
        "ip": "**********",
        "is_encryption_enabled": true,
        "is_firewall_enabled": true,
        "is_password_set": true,
        "java_version": null,
        "location": {
          "city": "New York",
          "country": "United States",
          "state": "New York",
          "longitude": -74.0060,
          "latitude": 40.7128
        },
        "os": "Windows",
        "os_version": "10.0",
        "security_agents": []
      },
      "adaptive_trust_assessments": {
        "on_demand": {
          "features": [
            {
              "key": "user_location",
              "name": "User Location",
              "score": 0.25
            },
            {
              "key": "device_state",
              "name": "Device Security Posture",
              "score": 0.6
            }
          ],
          "status": "completed",
          "threat_level": "medium",
          "trust_level": "not trusted"
        }
      },
      "alias": "",
      "application": {
        "key": "DIY12345ABCDEFGHIJKLM",
        "name": "Company VPN"
      },
      "auth_context": {
        "additional_context": "",
        "auth_stage": "mfa",
        "clientless": false,
        "trusted_endpoint": false
      },
      "auth_device": null,
      "email": "<EMAIL>",
      "event_type": "authentication",
      "factor": "duo_push",
      "isotimestamp": "2021-05-24T14:03:52.000Z",
      "ood_software": false,
      "reason": "timeout",
      "result": "denied",
      "timestamp": 1621875432,
      "trusted_endpoint_status": "not trusted",
      "txid": "5678efgh-5678-efgh-5678-5678efgh5678",
      "user": {
        "groups": [
          "VPN Users",
          "Sales"
        ],
        "key": "DUOUSER5678GHIJKL",
        "name": "<EMAIL>"
      },
      "policy_key": "DPMA1234567890ABCDEFG"
    }
  ],
  "metadata": {
    "next_offset": [1621875000, 0],
    "total_objects": 2
  }
}
```

### Trust Monitor Events Response

Below are complete examples of Trust Monitor events responses from the Duo Admin API, capturing all three event types (auth, bypass_status, and device_registration) as documented in the official API documentation:

```json
{
  "stat": "OK",
  "response": {
    "events": [
      {
        "explanations": [
          {
            "summary": "amanda_tucker has not logged in from this location recently.",
            "type": "NEW_COUNTRY_CODE"
          },
          {
            "summary": "amanda_tucker has not logged in from this IP recently.",
            "type": "NEW_NETBLOCK"
          },
          {
            "summary": "amanda_tucker has not accessed this application recently.",
            "type": "NEW_IKEY"
          }
        ],
        "from_common_netblock": true,
        "from_new_user": false,
        "low_risk_ip": false,
        "priority_event": true,
        "priority_reasons": [
          {
            "label": "CN",
            "type": "country"
          }
        ],
        "sekey": "SEDOR9BP00L23C6YUH5",
        "state": "new",
        "state_updated_timestamp": null,
        "surfaced_auth": {
          "access_device": {
            "browser": "Chrome",
            "browser_version": "86.0.4240.198",
            "epkey": "EP18JX1A10AB102M2T2X",
            "flash_version": null,
            "hostname": null,
            "ip": "************",
            "is_encryption_enabled": "unknown",
            "is_firewall_enabled": "unknown",
            "is_password_set": "unknown",
            "java_version": null,
            "location": {
              "city": "Shanghai",
              "country": "China",
              "state": "Shanghai"
            },
            "os": "Windows",
            "os_version": "10",
            "security_agents": "unknown"
          },
          "alias": "unknown",
          "application": {
            "key": "DIUD2X62LHMPDP00LXS3",
            "name": "Microsoft Azure Active Directory"
          },
          "auth_device": {
            "ip": null,
            "key": null,
            "location": {
              "city": null,
              "country": null,
              "state": null
            },
            "name": null
          },
          "email": "",
          "event_type": null,
          "factor": "not_available",
          "isotimestamp": "2020-11-17T03:19:13.092+00:00",
          "ood_software": "",
          "reason": "location_restricted",
          "result": "denied",
          "timestamp": 1605583153,
          "trusted_endpoint_status": null,
          "txid": "436694ad-467c-4aed-b048-8ad--f58e04c",
          "user": {
            "groups": [
              "crazy"
            ],
            "key": "DUN73JE5M92DP00L4ZYS",
            "name": "amanda_tucker"
          }
        },
        "surfaced_timestamp": 1605602911680,
        "triage_event_uri": "https://admin-xxxxxxxx.duosecurity.com/trust-monitor?sekey=SEDOR9BP00L23C6YUH5",
        "triaged_as_interesting": false,
        "type": "auth"
      },
      {
        "bypass_status_enabled": 1604337058989,
        "enabled_by": {
          "key": "DEWGH6P00LT2R0I60UI",
          "name": "Ellery Munson"
        },
        "enabled_for": {
          "key": "DUN73JE5M92DP00L4ZYS",
          "name": "amanda_tucker"
        },
        "priority_event": true,
        "priority_reasons": [],
        "sekey": "SEDOR9BP00L34C8YUH6",
        "state": "new",
        "state_updated_timestamp": null,
        "surfaced_timestamp": 1605602911680,
        "triaged_as_interesting": false,
        "type": "bypass_status"
      },
      {
        "explanations": [
          {
            "summary": "The registered device has an out-of-date version of the operating system installed.",
            "type": "REGISTER_OS_OUTDATED"
          }
        ],
        "from_new_user": false,
        "priority_event": false,
        "priority_reasons": [],
        "sekey": "SEDOR9BP00L23C6YUH7",
        "state": "new",
        "state_updated_timestamp": null,
        "surfaced_timestamp": 1675893605269,
        "triaged_as_interesting": false,
        "type": "device_registration"
      }
    ],
    "metadata": {
      "next_offset": "31229"
    }
  }
}
```

## OCSF Schema Mapping

### Target OCSF Class Selection

The most appropriate OCSF class for Cisco Duo authentication events is the **Authentication** class, which is found in the Identity and Access Management category. This class is specifically designed for authentication events and provides appropriate fields for capturing all relevant details from Duo's authentication logs.

For Duo Trust Monitor events, we recommend using the **Detection Finding** class from the Detection & Response category, as these events represent security detections that may require investigation.

### Detailed Field Mapping Table - Authentication Logs

| Source Field (API field) | OCSF Target Field (class: Authentication) | Ignored Reason | Description |
|---------------------------|-------------------------------------------|----------------|-------------|
| | class_uid = Authentication | | |
| | category_uid = Identity & Access Management | | |
| | map action and disposition from DI - Cisco Duo \| Authentication Log Reason to action and disposition mapping | | |
| access_device.browser | http_request.user_agent | | The web browser used for access. |
| access_device.browser_version | | N/A | The browser version. |
| access_device.ip | src_endpoint.ip | | Source IP address |
| access_device.browser_version | http_request.user_agent | | Combined with browser |
| access_device.epkey | src_endpoint.uid | | The endpoint's unique identifier. Collected during authentication events. Most reliable when reported by Duo Desktop installed on the endpoint. |
| access_device.flash_version | | N/A | The Flash plugin version used, if present, otherwise "uninstalled". |
| access_device.hostname | src_endpoint.hostname | | The hostname, if present, otherwise null. |
| access_device.ip | src_endpoint.ip | | The access device's IP address, if present, otherwise null. |
| access_device.is_encryption_enabled | | N/A | Reports the disk encryption state as detected by Duo Desktop. One of true, false, or "unknown". |
| access_device.is_firewall_enabled | | N/A | Reports the firewall state as detected by Duo Desktop. One of true, false, or "unknown". |
| access_device.is_password_set | | N/A | Reports the system password state as detected by Duo Desktop. One of true, false, or "unknown". |
| access_device.java_version | | N/A | Java version |
| access_device.location.city | src_endpoint.location.city | | City of source IP |
| access_device.location.country | src_endpoint.location.country | | The country name. Refer to ISO 3166 for a list of possible countries. |
| access_device.location.state | src_endpoint.location.region | | The state, county, province, or prefecture. |
| access_device.location.latitude | src_endpoint.location.lat | | Latitude of source IP |
| access_device.location.longitude | src_endpoint.location.long | | Longitude of source IP |
| access_device.os | src_endpoint.os.name | | Operating system name |
| access_device.os_version | src_endpoint.os.version | | Operating system version |
| access_device.security_agents | Add Agent to src_endpoint.agents_list | | Reports the security agents present on the endpoint as detected by Duo Desktop. |
| security_agent: | src_endpoint.agents_list.name | | |
| version: | src_endpoint.agents_list.version | | |
| | src_endpoint.agents_list.type = Endpoint Detection and Response (ID:1) | | |
| adaptive_trust_assessments | | | Complex object. Individual fields listed below |
| | | | Risk-based authentication information. Values present only when the application accessed features Duo's inline browser prompt. This information is available to Duo Premier and Duo Advantage plan customers. Type of adaptive trust assessment. One of: more_secure_auth: Trust assessment information for Risk-Based Factor Selection. remember_me: Trust assessment information for Risk-Based Remembered Devices. |
| adaptive_trust_assessments.detected_attack_detectors | | N/A | List of the risk-based authentication detections found during or after an authentication attempt. Only returned for more_secure_auth. |
| adaptive_trust_assessments.feature_version | | N/A | The feature version for the risk-based authentication trust assessment. |
| adaptive_trust_assessments.model_version | | N/A | The model version for the risk-based authentication trust assessment. |
| adaptive_trust_assessments.policy_enabled | | N/A | Denotes if risk-based authentication was enabled by the policy under which the trust assessment was evaluated. One of: true, false, or false (reserved for historical authentication logs that do not have the policy_enabled field populated). |
| adaptive_trust_assessments.reason | | N/A | The reason behind the trust assessment level. |
| adaptive_trust_assessments.trust_level | | N/A | The trust assessment level. One of: ERROR, LOW, NORMAL, UNKNOWN, or UNSET. |
| alias | user.uid_alt | | The username alias used to log in. No value if the user logged in with their username instead of a username alias. |
| application.key | service.uid | | Application identifier |
| application.name | service.name | | Application name |
| auth_device | | | Complex object. See fields listed below. |
| auth_device.ip | device.ip | | Information about the device used to approve or deny authentication. The IP address of the authentication device. |
| auth_device.key | device.uid | | The Duo identifier of the authentication device (the phone_id value for a phone, the webauthnkey value for a security key, etc.). |
| auth_device.location.city | device.location.city | | Auth device location city |
| auth_device.location.country | device.location.country | | Auth device location country |
| auth_device.location.state | device.location.region | | Auth device location state |
| auth_device.location.latitude | device.location.lat | | Auth device latitude |
| auth_device.location.longitude | device.location.long | | Auth device longitude |
| auth_device.name | device.name | | Authentication device name |
| email | user.email_addr | | The email address of the user, if known to Duo, otherwise none. |
| event_type | if authentication: activity = Logon (ID:1) | | The type of activity logged. one of: "authentication" or "enrollment". |
| | type_uid = Authentication: Logon | | |
| | if enrollment: activity = Preauth (ID:9) | | |
| | type_uid = Authentication: Preauth | | |
| factor | if Desktop Authenticator or duo_mobile_passcode_hotp or duo_mobile_passcode_totp or duo_mobile_passcode or yubikey_code: auth_factors.factor_type = OTP (ID:7) | | Authentication method used |
| | if duo_push or Duo Push (passwordless) or verified_duo_push or Verified Duo Push (passwordless): auth_factors.factor_type = Push Notification (ID:5) | | |
| | if not_available: auth_factors.factor_type = Unknown (ID:0) | | |
| | if hardware_token: auth_factors.factor_type = Hardware Token (ID:6) | | |
| | if sms_passcode or sms_refresh: auth_factors.factor_type = SMS (ID:1) | | |
| | if WebAuthn Chrome Touch ID or WebAuthn Credential or WebAuthn Security Key: auth_factors.factor_type = WebAuthn (ID:10) | | |
| | if phone_call: auth_factors.factor_type = Phone Call (ID:3) | | |
| | else: factor | | |
| | if duo_mobile_passcode_hotp: auth_factors.is_hotp = True | | |
| | if duo_mobile_passcode_totp: auth_factors.is_totp = True | | |
| | auth_factors.provider = "Cisco Duo" | | |
| isotimestamp | time | Redundant with time_dt | ISO 8601 format timestamp |
| ood_software | | N/A | If authentication was denied due to out-of-date software, shows the name of the software, i.e. "Chrome", "Flash", etc. No value if authentication was successful or authentication denial was not due to out-of-date software. |
| passport_assessment | | N/A | Information on whether the authentication supported Duo Passport. Will return for all authentications, even if Duo Passport is not enabled. Authentication logs before August 2024 will not return this field. |
| | is_supported | | If true, the authentication supported Duo Passport. If false, the authentication did not support Duo Passport. |
| | reason | | Returns supported if is_supported is true. Otherwise, returns a reason on why Duo Passport could not be used for that authentication. |
| reason | status_detail | | Provide the reason for the authentication attempt result. |
| result | if success: status = Success | | The result of the authentication attempt. One of: success, denied, failure, error, or fraud. |
| | if failure: status = Failure | | |
| | if denied: status = Denied | | |
| | if error: status = Error | | |
| | if fraud: status = Fraud | | |
| timestamp | if isotimestamp is not available, assign to time. | | An integer indicating the Unix timestamp of the event. |
| txid | metadata.uid | | Transaction ID for tracking related events |
| user.groups | user.groups.name | | User group memberships |
| user.key | user.uid | | Unique ID of the user |
| user.name | user.name | | User email address |

### Detailed Field Mapping Table - Trust Monitor Events

| Source Field (API field) | OCSF Target Field (Detection Finding) | Ignored Reason | Description |
|---------------------------|---------------------------------------|----------------|-------------|
| | finding_info.product.name = "Cisco Duo" | | |
| bypass_status_enabled | start_time | bypass_status_enabled is returned only when type=bypass_status | An integer indicating the Unix timestamp in milliseconds when bypass status was enabled for the user or group. Returned for events with type=bypass_status. |
| enabled_by | key: evidences.actor.user.uid | | The application or the administrator that enabled bypass status. Returned for events with type=bypass_status. |
| | value: evidences.actor.user.name | | |
| enabled_for | key: evidences.user.uid | | The user or group with bypass status. Returned for events with type=bypass_status. |
| | value: evidences.user.name | | |
| explanations | for each explanation, add Anomaly object to anomaly_analyses.anomalies: | | An array of objects describing why Trust Monitor surfaced the event. The summary provides additional details. |
| | summary: anomaly_analyses.anomalies.observations.value | | |
| | type: anomaly_analyses.anomalies.observation_pattern | | |
| | | | Events with type=auth: The type value will be one of: GRANTED_AUTH, NEW_COUNTRY_CODE, NEW_DEVICE, NEW_FACTOR, NEW_NETBLOCK, UNREALISTIC_GEOVELOCITY, UNUSUAL_COUNTRY_CODE, UNUSUAL_DEVICE, UNUSUAL_FACTOR, UNUSUAL_NETBLOCK, UNUSUAL_TIME_OF_DAY, or USER_MARKED_FRAUD. Events with type=device_registration: The type value will be one of: REGISTER_INACTIVE_USER, REGISTER_OS_OUTDATED, REGISTER_UNLOCK, or REGISTER_TAMPERED. |
| from_common_netblock | | N/A | A boolean describing if this event was created from a common IP netblock. Either true or false. Returned for events with type=auth. |
| from_new_user | If type=auth or device_registration, set the following Authentication field on object added related events: | N/A | Returned for events with type=auth or type=device_registration. |
| | is_new_logon = True | | A boolean describing if this event was created for a new user. Either true or false. |
| low_risk_ip | | N/A | A boolean describing if this event was created from an IP address identified in the Risk Profile configuration as a low risk IP address. Either true or false. Returned for events with type=auth. |
| priority_event | | N/A | A boolean describing if the event matches the Risk Profile configuration. Either true or false. |
| priority_reasons | | N/A | An array of objects describing how the event matches the Trust Monitor Risk Profile configuration. Each object contains: type: The type of priority reason for the event's match. label: The label of the priority reason for the event's match. Returned for events with type=auth or type=device_registration. |
| sekey | finding_info.uid | | The unique identifier for this event as a 20 character string. This is unique across all different event types. |
| | metadata.uid | | |
| state | if new: status = New (ID: 1) | | A string describing the state of the event. One of state new or state processed. |
| | if processed: status = Resolved (ID:4) | | |
| state_updated_timestamp | finding_info.modified_time | | An integer indicating the Unix timestamp in milliseconds of the last change to the state of the event. |
| surfaced_auth | Add Related Event object to finding_info.related_events | | An object which represents the actual authentication. See the Authentication Logs response format for authentication event details. Returned for events with type=auth. |
| | Use Authentication log mappings to create authentication object. | | |
| | finding_info.related_events.uid = authentication.metadata.uid | | |
| | finding_info.related_events.type_uid = authentication.type_uid | | |
| | finding_info.related_events.type= authentication.type_name | | |
| | authentication.metadata.correlation_uid = sekey | | |
| surfaced_timestamp | time | | An integer indicating the Unix timestamp in milliseconds when the event was surfaced by Trust Monitor. |
| triaged_as_interesting | is_suspected_breach | | A boolean describing if this event was triaged as being interesting or not interesting. Either true or false. |
| type | metadata.event_code | | The type of event, as a string. One of auth, bypass_status, or device_registration. |
| | if auth: message = "Cisco Duo Trust Monitor Authentication Anomaly detected" | | |
| | if bypass_status: message = "Cisco Duo Trust Monitor bypass status event detected" | | |
| | if device_registration: message = "Cisco Duo Trust Monitor device registration anomaly detected" | | |
| | Join explanations.keys separated by . | | |
| | ioc.external_id (Event.ioc.external_id) | | If explanations is not empty or NA, use type. |
| | Trust Monitor Anomaly Detected - Join explanations.value by , | | Explanation keys contains codes that explain the reason why the event was detected. Bypass enabled events do not contain explanations so use type. |
| | ioc.external_name (Event.ioc.external_name) | | |
| | If type = bypass_status: Trust Monitor bypass status enabled. | | |
| | False | | We should create IOC Templates because they never have a product definition. |
| | ioc.has_ioc_definition (ioc.has_ioc_definition) | | |

## Authentication Log Reason to action and disposition mapping

| Reason | action | disposition | Description |
|--------|--------|-------------|-------------|
| user_marked_fraud | Denied | Alert | Return events where authentication was denied because the end user explicitly marked "fraudulent". |
| deny_unenrolled_user | Denied | Unauthorized | Return events where authentication was denied because of the following policy: "deny not enrolled users". |
| error | Denied | Error | Return events where authentication was denied because of an error. |
| locked_out | Denied | Delayed | Return events generated by users that are locked out. |
| user_disabled | Denied | Access Revoked | Return events where authentication was denied because the user was disabled. |
| user_cancelled | Denied | Dropped | Return events where authentication was denied because the end user cancelled the request. |
| invalid_passcode | Denied | Unauthorized | Return events where authentication was denied because the passcode was invalid. |
| no_response | Denied | No Action | Return events where authentication was denied because there was no response from the user. |
| no_keys_pressed | Denied | No Action | Return events where authentication was denied because no keys were pressed to accept the auth. |
| call_timed_out | Denied | No Action | Return events where authentication was denied because the call was not answered or call authentication timed out for an indeterminate reason. |
| location_restricted | Denied | Blocked | Return events where authentication was denied because the end user's location was restricted. |
| factor_restricted | Denied | Rejected | Return events where authentication was denied because the authentication method used was not allowed. |
| platform_restricted | Denied | Rejected | Return events where authentication was denied because the access platform was not allowed. |
| version_restricted | Denied | Rejected | Return events where authentication was denied because the software version was not allowed. |
| rooted_device | Denied | Rejected | Return events where authentication was denied because the approval device was rooted. |
| no_screen_lock | Denied | Rejected | Return events where authentication was denied because the approval device does not have screen lock enabled. |
| touch_id_disabled | Denied | Rejected | Return events where authentication was denied because the approval device's biometrics (fingerprint, Face ID or Touch ID) is disabled. |
| no_disk_encryption | Denied | Rejected | Return events where authentication was denied because the approval device did not have disk encryption enabled. |
| anonymous_ip | Denied | Rejected | Return events where authentication was denied because the authentication request came from an anonymous IP address. |
| out_of_date | Denied | Rejected | Return events where authentication was denied because the software was out of date. |
| denied_by_policy | Denied | Unauthorized | Return events where authentication was denied because of a policy. |
| software_restricted | Denied | Blocked | Return events where authentication was denied because of software restriction. |
| no_duo_certificate_present | Denied | Unauthorized | Return events where authentication was denied because there was no Duo certificate present. |
| user_provided_invalid_certificate | Denied | Unauthorized | Return events where authentication was denied because an invalid management certificate was provided. |
| could_not_determine_if_endpoint_was_trusted | Denied | Unauthorized | Return events where authentication was denied because it could not be determined if the endpoint was trusted. |
| frequent_attempts | Denied | Blocked | Return events where authentication was denied because of frequent attempts. |
| invalid_management_certificate_collection_state | Denied | Unauthorized | Return events where authentication was denied because of an invalid management certificate collection state. |
| no_referring_hostname_provided | Denied | Unauthorized | Return events where authentication was denied because no referring hostname was provided. |
| invalid_referring_hostname_provided | Denied | Unauthorized | Return events where authentication was denied because an invalid referring hostname was provided. |
| no_web_referer_match | Denied | Unauthorized | Return events where authentication was denied because an invalid referring hostname did not match an application's hostnames list. |
| endpoint_failed_google_verification | Denied | Unauthorized | Return events where authentication was denied because the endpoint failed Google verification. |
| endpoint_is_not_trusted | Denied | Unauthorized | Return events where authentication was denied because the endpoint was not trusted. |
| invalid_device | Denied | Rejected | Return events where authentication was denied because the device was invalid. |
| endpoint_is_not_in_management_system | Denied | Unauthorized | Return events where authentication was denied because the endpoint is not in a management system. |
| no_activated_duo_mobile_account | Denied | Unauthorized | Return events where authentication was denied because the end user does not have an activated Duo Mobile app account. |
| queued_inflight_auth_expired | Denied | Delayed | Return events where authentication was denied when more authentications than the number allowed by the lockout threshold are started simultaneously. The authentications past the threshold are queued, and then removed from the queue after enough failures trigger a lockout. |
| allow_unenrolled_user | Allowed | Allowed | Return events where authentication was successful because of the following policy: "allow not enrolled users". |
| bypass_user | Allowed | Allowed | Return events where authentication was successful because a bypass code was used. |
| trusted_network | Allowed | Allowed | Return events where authentication was successful because the end user was on a trusted network. |
| remembered_device | Allowed | Allowed | Return events where authentication was successful because the end user was on a remembered device. |
| trusted_location | Allowed | Allowed | Return events where authentication was successful because the end user was in a trusted location. |
| user_approved | Allowed | Allowed | Return events where authentication was successful because the end user approved the authentication request. |
| valid_passcode | Allowed | Allowed | Return events where authentication was successful because the end user used a valid passcode. |
| allowed_by_policy | Allowed | Allowed | Return events where authentication was successful because of a policy. |
| allow_unenrolled_user_on_trusted_network | Allowed | Allowed | Return events where authentication was successful because the unenrolled user's access device was on an authorized network. |
| user_not_in_permitted_group | Allowed | Allowed | Return events where authentication was denied because the user did not belong to one of the Permitted Groups specified in the application's settings. |
| verification_code_correct | Allowed | Allowed | Return events where authentication was successful because of a Verified Duo Push. |
| verification_code_missing | Denied | Rejected | Return events where authentication was denied because the user used an old version of Duo Mobile that does not support Verified Duo Push. |
| verification_code_incorrect | Denied | Unauthorized | Return events where authentication was denied because the user entered the wrong code when approving a Verified Duo Push. |

## Implementation Notes

### Special Handling Requirements

1. **Timestamp Conversion**: Duo provides timestamps as Unix timestamps (seconds since epoch) and ISO format. The ISO format (`isotimestamp`) should be preferred for OCSF's `time_dt` field.

2. **Trust Monitor Events Processing**:
   - Trust Monitor events should be processed separately from authentication logs, as they represent security detections rather than authentication attempts.
   - These events should be mapped to the Detection Finding OCSF class.
   - The `historical_logins` array provides valuable context for understanding the detection and should be included in the OCSF `context_data` field.
   - Some Trust Monitor events contain integration and group information that should be mapped to appropriate OCSF fields.

3. **Multiple Users in Trust Monitor Events**: Some Trust Monitor events might contain multiple affected users in the `users` array. Create separate Detection Finding events for each affected user to ensure proper attribution and alerting.

4. **Trust Monitor Pagination**: When retrieving Trust Monitor events, implement pagination handling using the `next_offset` value returned in the response metadata. This is a tuple containing `[timestamp, entry_offset]` that must be passed as-is to subsequent API calls.

### PII and Sensitive Data Handling

The following fields contain PII and should be handled according to data privacy requirements:
- User email addresses (`user.name`, `email`)
- IP addresses (`access_device.ip` and `auth_device.ip`)
- Device names (`auth_device.name`)
- Geographic location information (coordinates, city, etc.)
- Group memberships (`user.groups` and `groups[].name`)

### Assumptions and Decisions

1. Authentication logs are the primary focus for this integration, with Trust Monitor events as a secondary enhancement.

2. We map all Duo authentication attempts to the OCSF LOGON activity, as Duo is primarily used for authentication rather than logoff events.

3. For fields like `access_device.is_encryption_enabled` that don't have direct OCSF mappings in the standard schema, we include them in the `unmapped.device_security` object as custom fields to preserve the security context they provide.

4. Trust Monitor events are mapped to the OCSF Detection Finding class instead of the Authentication class, as they represent security detections rather than authentication attempts themselves.

5. For all Trust Monitor events, we set the status to NEW.

## References

1. **API Documentation**
   - Cisco Duo Admin API Documentation
   - Authentication Logs API Reference
   - Trust Monitor API Reference

2. **OCSF Schema References**
   - OCSF Authentication Class Documentation
   - OCSF Detection Finding Class Documentation
   - OCSF Identity and Access Management Category

3. **Similar Integration Examples**
   - Okta integration in our codebase
   - Microsoft Entra ID (Azure AD) integration patterns

---

© 2025 Critical Start All rights reserved

